import type { NextConfig } from 'next'
import withPWA from 'next-pwa'

const nextConfig: NextConfig = {
  /* config options here */
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Next.js requires unsafe-inline and unsafe-eval
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", // Tailwind and Google Fonts require unsafe-inline
              "img-src 'self' data: https: blob:", // Allow images from various sources
              "connect-src 'self' https://*.supabase.co https://*.supabase.io", // Supabase API connections
              "frame-src https://www.youtube.com https://www.tiktok.com", // Video embeds
              "font-src 'self' data: https://fonts.gstatic.com", // Google Fonts and data URLs
              "media-src 'self' blob:", // Media files
              "object-src 'none'", // Block object embeds for security
              "base-uri 'self'", // Restrict base URI
              "form-action 'self'", // Restrict form submissions
              "frame-ancestors 'none'" // Prevent embedding in frames
            ].join('; ')
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ],
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript; charset=utf-8',
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
    ]
  },
}

const pwaConfig = withPWA({
  dest: 'public',
  disable: process.env.NODE_ENV === 'development',
  register: true,
  skipWaiting: true,
  workboxOptions: {
    swSrc: 'public/sw.js',
    swDest: 'public/sw.js',
    importScripts: ['workbox-v6.5.4/workbox-sw.js'],
  },
})(nextConfig)

export default pwaConfig
