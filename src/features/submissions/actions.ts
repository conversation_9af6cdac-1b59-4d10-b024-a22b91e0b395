'use server'

import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
// import { redirect } from 'next/navigation' // Import redirect
import type { z } from 'zod/v4'
import { submissionSchema } from './submissionSchema'; // Assuming this is the Zod schema

// Define the shape of the state object returned by the action
export interface SubmissionActionState {
  success?: boolean
  message?: string
  error?: string
  submissionId?: bigint | number // Match return type of RPC
  validationErrors?: z.ZodIssue[] // More detailed validation errors
}

// Allowlist of trusted video domains to prevent SSRF attacks
const ALLOWED_VIDEO_DOMAINS = [
  'youtube.com',
  'youtu.be',
  'www.youtube.com',
  'tiktok.com',
  'www.tiktok.com',
  'vm.tiktok.com',
  'instagram.com',
  'www.instagram.com',
  'vimeo.com',
  'www.vimeo.com'
];

/**
 * Validates video URL against allowlist to prevent SSRF attacks
 * Only allows HTTPS URLs from trusted video platforms
 */
export function validateVideoUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);

    // Only allow HTTPS protocol for security
    if (urlObj.protocol !== 'https:') {
      return false;
    }

    // Check if hostname is in our allowlist
    const hostname = urlObj.hostname.toLowerCase();
    return ALLOWED_VIDEO_DOMAINS.some(domain =>
      hostname === domain || hostname.endsWith(`.${domain}`)
    );
  } catch {
    // Invalid URL format
    return false;
  }
}

export async function submitPerformance(
  prevState: SubmissionActionState | undefined, // Can be undefined for initial state
  formData: FormData,
): Promise<SubmissionActionState> {
  const cookieStore = await cookies() // Await cookies() as it seems to return a Promise
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // 1. Authentication Check
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser()

  if (authError || !user) {
    return { error: 'Authentication required. Please log in.' }
  }
  const userId = user.id

  // 2. Parse and Validate Form Data
  // Convert FormData to an object suitable for Zod parsing
  const rawFormData: Record<string, unknown> = {}
  formData.forEach((value, key) => {
    // Handle multiple values for the same key if necessary, though unlikely for this form
    rawFormData[key] = value
  })

  const validationResult = submissionSchema.safeParse(rawFormData)

  if (!validationResult.success) {
    console.error('Validation errors:', validationResult.error.issues)
    return {
      error: 'Invalid form data. Please check your inputs.',
      // Providing field-specific errors can be useful for the UI
      validationErrors: validationResult.error.issues,
    }
  }

  const { exerciseId, videoUrl, weightLifted, notes } = validationResult.data
  // submissionSchema should ideally coerce exerciseId and weightLifted to numbers.
  // If they are still strings, ensure conversion:
  const numericExerciseId =
    typeof exerciseId === 'string'
      ? Number.parseInt(exerciseId, 10)
      : exerciseId
  const numericWeightLifted =
    typeof weightLifted === 'string'
      ? Number.parseFloat(weightLifted)
      : weightLifted

  if (Number.isNaN(numericExerciseId) || Number.isNaN(numericWeightLifted)) {
    return { error: 'Exercise ID and Weight Lifted must be numbers.' }
  }

  // 3. Fetch User Profile for Role and Last Submission Time
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role, last_submission_at')
    .eq('id', userId)
    .single()

  if (profileError || !profile) {
    console.error('Profile fetch error:', profileError)
    return { error: 'Failed to fetch user profile. Please try again.' }
  }

  // 4. Rate Limit Check (for non-admins)
  if (profile.role !== 'admin') {
    if (profile.last_submission_at) {
      const lastSubmissionDate = new Date(profile.last_submission_at)
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      if (lastSubmissionDate > twentyFourHoursAgo) {
        return {
          error:
            'Submission limit reached. Please wait 24 hours before submitting again.',
        }
      }
    }
  }

  // 5. Video URL Validation (Secure - SSRF Protection)
  if (!validateVideoUrl(videoUrl)) {
    return {
      error: 'Invalid video URL. Only HTTPS URLs from YouTube, TikTok, Instagram, and Vimeo are supported.'
    }
  }

  // 6. Database Insert (using RPC function)
  const rpcParams = {
    user_id_param: userId,
    exercise_id_param: numericExerciseId,
    video_url_param: videoUrl,
    weight_lifted_param: numericWeightLifted,
    notes_param: notes || null, // Ensure notes is null if empty/undefined
  }

  const { data: newSubmissionId, error: rpcError } = await supabase.rpc(
    'create_submission_and_update_profile',
    rpcParams,
  )

  if (rpcError) {
    console.error('RPC error (create_submission_and_update_profile):', rpcError)
    return {
      error: `Failed to submit performance: ${rpcError.message}. Please try again.`,
    }
  }

  // 7. Success Handling
  revalidatePath('/account') // Revalidate path for submission history
  revalidatePath('/submissions') // Revalidate any general submissions list page
  // Don't call redirect() here if you want to return a state for the form to show a toast first.
  // The form will handle the redirect in a useEffect.
  return {
    success: true,
    message: 'Performance submitted successfully!',
    submissionId: newSubmissionId,
  }
}
