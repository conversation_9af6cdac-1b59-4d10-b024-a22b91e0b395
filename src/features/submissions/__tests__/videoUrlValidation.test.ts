import { describe, expect, it } from 'vitest'
import { validateVideoUrl } from '../actions'

describe('Video URL Validation - SSRF Security Tests', () => {
  describe('Valid URLs', () => {
    it('should accept valid YouTube URLs', () => {
      const validUrls = [
        'https://youtube.com/watch?v=test',
        'https://www.youtube.com/watch?v=test',
        'https://youtu.be/test',
        'https://m.youtube.com/watch?v=test',
        'https://music.youtube.com/watch?v=test',
        'https://gaming.youtube.com/watch?v=test',
      ]

      for (const url of validUrls) {
        expect(validateVideoUrl(url)).toBe(true)
      }
    })

    it('should accept valid TikTok URLs', () => {
      const validUrls = [
        'https://tiktok.com/@user/video/123',
        'https://www.tiktok.com/@user/video/123',
        'https://vm.tiktok.com/test',
      ]

      for (const url of validUrls) {
        expect(validateVideoUrl(url)).toBe(true)
      }
    })

    it('should accept valid Instagram URLs', () => {
      const validUrls = [
        'https://instagram.com/p/test',
        'https://www.instagram.com/p/test',
      ]

      for (const url of validUrls) {
        expect(validateVideoUrl(url)).toBe(true)
      }
    })

    it('should accept valid Vimeo URLs', () => {
      const validUrls = [
        'https://vimeo.com/123456',
        'https://www.vimeo.com/123456',
      ]

      for (const url of validUrls) {
        expect(validateVideoUrl(url)).toBe(true)
      }
    })
  })

  describe('Security - SSRF Prevention', () => {
    it('should reject HTTP URLs (require HTTPS)', () => {
      const httpUrls = [
        'http://youtube.com/watch?v=test',
        'http://tiktok.com/@user/video/123',
        'http://instagram.com/p/test',
        'http://vimeo.com/123456',
      ]

      for (const url of httpUrls) {
        expect(validateVideoUrl(url)).toBe(false)
      }
    })

    it('should reject SSRF attack URLs', () => {
      const maliciousUrls = [
        'https://***************/latest/meta-data/',
        'https://localhost:5432/admin',
        'https://internal-service.company.com/secrets',
        'https://127.0.0.1:8080/admin',
        'https://***********/config',
        'https://********/internal',
        'https://example.com/video.mp4', // Not in allowlist
        'https://malicious-site.com/fake-video',
        'https://evil.com/attack',
      ]

      for (const url of maliciousUrls) {
        expect(validateVideoUrl(url)).toBe(false)
      }
    })

    it('should reject invalid URL formats', () => {
      const invalidUrls = [
        'not-a-url',
        'ftp://youtube.com/video',
        'javascript:alert(1)',
        'data:text/html,<script>alert(1)</script>',
        '',
        'https://',
        'https://.',
        'file:///etc/passwd',
      ]

      for (const url of invalidUrls) {
        expect(validateVideoUrl(url)).toBe(false)
      }
    })

    it('should reject domain spoofing attempts', () => {
      const spoofingUrls = [
        'https://youtube.com.evil.com/video',
        'https://evilyoutube.com/video',
        'https://youtube-com.evil.com/video',
        'https://tiktok.evil.com/video',
        'https://fake-youtube.com/video',
        'https://youtub3.com/video',
        'https://youtu.be.evil.com/video',
      ]

      for (const url of spoofingUrls) {
        expect(validateVideoUrl(url)).toBe(false)
      }
    })

    it('should handle edge cases correctly', () => {
      // Test case sensitivity
      expect(validateVideoUrl('https://YOUTUBE.COM/watch?v=test')).toBe(true)
      expect(validateVideoUrl('https://YouTube.com/watch?v=test')).toBe(true)
      
      // Test with ports (should be rejected as not in our allowlist format)
      expect(validateVideoUrl('https://youtube.com:443/watch?v=test')).toBe(true) // Standard HTTPS port
      expect(validateVideoUrl('https://youtube.com:8080/watch?v=test')).toBe(true) // Non-standard port but still youtube.com
      
      // Test with query parameters and fragments
      expect(validateVideoUrl('https://youtube.com/watch?v=test&t=30s')).toBe(true)
      expect(validateVideoUrl('https://youtube.com/watch?v=test#t=30s')).toBe(true)
      
      // Test with paths
      expect(validateVideoUrl('https://youtube.com/embed/test')).toBe(true)
      expect(validateVideoUrl('https://youtube.com/v/test')).toBe(true)
    })
  })
})
